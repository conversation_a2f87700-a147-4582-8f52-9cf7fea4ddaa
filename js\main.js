/**
 * 主程序入口
 */
import BalanceChart from "./components/BalanceChart.js";
import CPSChart from "./components/CPSChart.js";
import InfoDisplay from "./components/InfoDisplay.js";
import SectionTable from "./components/SectionTable.js";
import SolarChart from "./components/SolarChart.js";
import WindChart from "./components/WindChart.js";
import * as data from "./data.js";

// 确保布局在窗口大小变化时保持比例
function ensureLayoutProportions() {
  // 处理所有的charts-row布局
  const chartsRows = document.querySelectorAll(".charts-row");
  chartsRows.forEach((chartsRow) => {
    const totalWidth = chartsRow.offsetWidth;
    const gap = 20; // 与CSS中的gap值保持一致
    const halfWidth = (totalWidth - gap) / 2;

    // 处理第一行（平衡曲线和风电/光伏消纳曲线）
    if (chartsRow.querySelector(".balance-chart")) {
      const balanceChart = chartsRow.querySelector(".balance-chart");
      const chartsColumn = chartsRow.querySelector(".charts-column");

      if (balanceChart && chartsColumn) {
        balanceChart.style.width = `${halfWidth}px`;
        balanceChart.style.minWidth = `${halfWidth}px`;
        balanceChart.style.maxWidth = `${halfWidth}px`;

        chartsColumn.style.width = `${halfWidth}px`;
        chartsColumn.style.minWidth = `${halfWidth}px`;
        chartsColumn.style.maxWidth = `${halfWidth}px`;
      }
    }

    // 处理第二行（断面监视和CPS/信息显示）
    if (chartsRow.querySelector(".section-table")) {
      const sectionTable = chartsRow.querySelector(".section-table");
      const rightColumn = chartsRow.querySelector(".right-column");

      if (sectionTable && rightColumn) {
        sectionTable.style.width = `${halfWidth}px`;
        sectionTable.style.minWidth = `${halfWidth}px`;
        sectionTable.style.maxWidth = `${halfWidth}px`;

        rightColumn.style.width = `${halfWidth}px`;
        rightColumn.style.minWidth = `${halfWidth}px`;
        rightColumn.style.maxWidth = `${halfWidth}px`;
      }
    }
  });

  // 调整所有图表大小
  if (window.powerCharts) {
    window.powerCharts.balanceChart.chart.resize();
    window.powerCharts.windChart.chart.resize();
    window.powerCharts.solarChart.chart.resize();
    window.powerCharts.cpsChart.chart.resize();
  }
}

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 初始化平衡曲线图表
  const balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  const windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  const solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  const sectionTable = new SectionTable("section-table", data.sectionData);

  // 初始化CPS曲线图表
  const cpsChart = new CPSChart("cps-chart", data.cpsData);

  // 初始化信息显示
  const infoDisplay = new InfoDisplay("info-container", data.infoData);

  // 导出组件实例，方便后续可能的交互
  window.powerCharts = {
    balanceChart,
    windChart,
    solarChart,
    sectionTable,
    cpsChart,
    infoDisplay,
  };

  console.log("全网平衡监视系统初始化完成");

  // 初始化时确保布局比例
  // ensureLayoutProportions();

  // 添加窗口大小变化的监听器
  // window.addEventListener("resize", ensureLayoutProportions);
});

// 模拟数据更新示例（实际项目中可能通过API获取实时数据）

// 启动模拟数据更新
// simulateDataUpdate();
