/**
 * 断面监视表格组件
 */
class SectionTable {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error('容器元素不存在');
      return;
    }

    this.render();
  }

  render() {
    const { columns, data } = this.data;
    
    // 创建表格元素
    const table = document.createElement('table');
    
    // 创建表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column;
      headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // 创建表格主体
    const tbody = document.createElement('tbody');
    
    data.forEach(row => {
      const tr = document.createElement('tr');
      
      row.forEach(cell => {
        const td = document.createElement('td');
        td.textContent = cell;
        tr.appendChild(td);
      });
      
      tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    
    // 清空容器并添加表格
    this.container.innerHTML = '';
    this.container.appendChild(table);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }
}

export default SectionTable;