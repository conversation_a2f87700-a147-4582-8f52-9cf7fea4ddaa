html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}
body {
  background-color: #020f22;
  color: #c0c0c0;
  margin: 0;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 15px;
  height: 100vh;
}

header h1 {
  text-align: center;
  color: #fff;
  font-size: 2em;
  margin-bottom: 20px;
}

.main-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-grow: 1;
}

.charts-row {
  display: flex;
  gap: 10px;
  width: 100%;
  flex-wrap: nowrap;
}

.balance-chart {
  width: 60%;
}

.charts-column {
  display: flex;
  flex-direction: column;
  width: 40%;
  gap: 10px;
  width: 40%;
}

.chart-container,
.table-container,
.info-container {
  background-color: #0a1a33;
  padding: 15px;
  position: relative;
}

/* 移除不再使用的large-chart样式 */
/* .large-chart {
    grid-column: span 2;
} */

/* 断面监视表格样式 */
.section-table {
  width: 60%;
}

/* 右侧列样式 */
.right-column {
  width: 40%;
}

.chart-title {
  color: #fff;
  font-size: 1.2em;
  margin-bottom: 10px;
  border-left: 4px solid #00aeff;
  padding-left: 10px;
}

.chart {
  height: 200px;
}

#section-table table {
  width: 100%;
  border-collapse: collapse;
}

#section-table th,
#section-table td {
  border: 1px solid #1a3a66;
  padding: 8px;
  text-align: center;
}

#section-table th {
  background-color: #1a3a66;
  color: #fff;
}

.info-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.info-box {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-box-title {
  font-size: 1em;
  color: #c0c0c0;
  margin-bottom: 5px;
}

.info-box-value {
  font-size: 1.5em;
  color: #fff;
  font-weight: bold;
}

/* 图例样式 */
.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9em;
}

.legend-color {
  width: 15px;
  height: 3px;
  margin-right: 5px;
}

/* 数据标签样式 */
.data-label {
  position: absolute;
  background-color: rgba(10, 26, 51, 0.7);
  border: 1px solid #1a3a66;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
  color: #fff;
  pointer-events: none;
}

/* 当前值标记样式 */
.current-value {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid;
}
